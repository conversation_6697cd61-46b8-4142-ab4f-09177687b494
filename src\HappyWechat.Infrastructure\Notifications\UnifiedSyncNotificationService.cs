using Microsoft.AspNetCore.SignalR;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.DependencyInjection;
using HappyWechat.Infrastructure.Notifications.Interfaces;
using HappyWechat.Infrastructure.Notifications.Models;
using System.Diagnostics;
using System.Collections.Concurrent;

namespace HappyWechat.Infrastructure.Notifications;

/// <summary>
/// 统一同步通知服务实现
/// 提供可靠的、统一的同步通知机制
/// </summary>
public class UnifiedSyncNotificationService : IUnifiedSyncNotificationService
{
    private readonly IServiceProvider _serviceProvider;
    private readonly ILogger<UnifiedSyncNotificationService> _logger;
    private readonly NotificationStatistics _statistics;
    private readonly ConcurrentDictionary<string, DateTime> _lastNotificationTimes;
    private readonly SemaphoreSlim _sendSemaphore;

    // 默认发送选项
    private static readonly NotificationSendOptions DefaultOptions = new()
    {
        EnableRetry = true,
        MaxRetryCount = 3,
        RetryIntervalMs = 1000,
        EnableFallback = true,
        Priority = NotificationPriority.Normal
    };

    public UnifiedSyncNotificationService(
        IServiceProvider serviceProvider,
        ILogger<UnifiedSyncNotificationService> logger)
    {
        _serviceProvider = serviceProvider;
        _logger = logger;
        _statistics = new NotificationStatistics();
        _lastNotificationTimes = new ConcurrentDictionary<string, DateTime>();
        _sendSemaphore = new SemaphoreSlim(10, 10); // 限制并发发送数量
    }

    /// <summary>
    /// 发送联系人同步进度通知
    /// </summary>
    public async Task<NotificationSendResult> SendContactSyncProgressAsync(
        SyncProgressNotification notification, 
        NotificationSendOptions? options = null)
    {
        options ??= DefaultOptions;
        
        // 防抖：避免过于频繁的进度通知
        var debounceKey = $"contact_progress_{notification.WxManagerId}";
        if (ShouldDebounce(debounceKey, TimeSpan.FromMilliseconds(500)))
        {
            return NotificationSendResult.CreateSuccess(0, 0, false);
        }

        return await SendNotificationWithRetryAsync(
            "ContactSyncProgress",
            notification.WxManagerId,
            new
            {
                notification.WxManagerId,
                notification.Status,
                notification.ProgressPercentage,
                notification.ProcessedCount,
                notification.TotalCount,
                notification.CurrentPhase,
                notification.EstimatedRemainingMs,
                notification.Timestamp,
                notification.NotificationId
            },
            options);
    }

    /// <summary>
    /// 发送联系人同步完成通知
    /// </summary>
    public async Task<NotificationSendResult> SendContactSyncCompletedAsync(
        ContactSyncCompletionNotification notification, 
        NotificationSendOptions? options = null)
    {
        options ??= DefaultOptions;
        options.Priority = NotificationPriority.High; // 完成通知优先级高

        _logger.LogInformation("🎉 发送联系人同步完成通知 - WxManagerId: {WxManagerId}, Success: {Success}, Failed: {Failed}",
            notification.WxManagerId, notification.SuccessCount, notification.FailedCount);

        var result = await SendNotificationWithRetryAsync(
            "ContactSyncCompleted",
            notification.WxManagerId,
            new
            {
                notification.WxManagerId,
                notification.Status,
                notification.SuccessCount,
                notification.FailedCount,
                notification.FriendsSuccessCount,
                notification.EnterpriseSuccessCount,
                notification.FriendsFailedCount,
                notification.EnterpriseFailedCount,
                notification.ElapsedMilliseconds,
                notification.CompletedTime,
                notification.SessionId,
                notification.ErrorMessage,
                notification.NotificationId
            },
            options);

        // 同时发送数据变更通知
        await SendDataChangedNotificationAsync(notification.WxManagerId, "Contact", "ContactSyncCompleted");

        return result;
    }

    /// <summary>
    /// 发送群组同步进度通知
    /// </summary>
    public async Task<NotificationSendResult> SendGroupSyncProgressAsync(
        SyncProgressNotification notification, 
        NotificationSendOptions? options = null)
    {
        options ??= DefaultOptions;
        
        // 防抖：避免过于频繁的进度通知
        var debounceKey = $"group_progress_{notification.WxManagerId}";
        if (ShouldDebounce(debounceKey, TimeSpan.FromMilliseconds(500)))
        {
            return NotificationSendResult.CreateSuccess(0, 0, false);
        }

        return await SendNotificationWithRetryAsync(
            "GroupSyncProgress",
            notification.WxManagerId,
            new
            {
                notification.WxManagerId,
                notification.Status,
                notification.ProgressPercentage,
                notification.ProcessedCount,
                notification.TotalCount,
                notification.CurrentPhase,
                notification.EstimatedRemainingMs,
                notification.Timestamp,
                notification.NotificationId
            },
            options);
    }

    /// <summary>
    /// 发送群组同步完成通知
    /// </summary>
    public async Task<NotificationSendResult> SendGroupSyncCompletedAsync(
        GroupSyncCompletionNotification notification, 
        NotificationSendOptions? options = null)
    {
        options ??= DefaultOptions;
        options.Priority = NotificationPriority.High; // 完成通知优先级高

        _logger.LogInformation("🎉 发送群组同步完成通知 - WxManagerId: {WxManagerId}, Success: {Success}, Failed: {Failed}",
            notification.WxManagerId, notification.SuccessCount, notification.FailedCount);

        var result = await SendNotificationWithRetryAsync(
            "GroupSyncCompleted",
            notification.WxManagerId,
            new
            {
                notification.WxManagerId,
                notification.Status,
                notification.SuccessCount,
                notification.FailedCount,
                notification.GroupCount,
                notification.NewGroupCount,
                notification.UpdatedGroupCount,
                notification.ElapsedMilliseconds,
                notification.CompletedTime,
                notification.SessionId,
                notification.ErrorMessage,
                notification.NotificationId
            },
            options);

        // 同时发送数据变更通知
        await SendDataChangedNotificationAsync(notification.WxManagerId, "Group", "GroupSyncCompleted");

        return result;
    }

    /// <summary>
    /// 发送同步失败通知
    /// </summary>
    public async Task<NotificationSendResult> SendSyncFailedAsync(
        Guid wxManagerId, 
        string syncType, 
        string errorMessage, 
        NotificationSendOptions? options = null)
    {
        options ??= DefaultOptions;
        options.Priority = NotificationPriority.Critical; // 失败通知优先级最高

        _logger.LogError("❌ 发送同步失败通知 - WxManagerId: {WxManagerId}, Type: {SyncType}, Error: {Error}",
            wxManagerId, syncType, errorMessage);

        return await SendNotificationWithRetryAsync(
            "SyncFailed",
            wxManagerId,
            new
            {
                WxManagerId = wxManagerId,
                SyncType = syncType,
                ErrorMessage = errorMessage,
                Timestamp = DateTime.UtcNow,
                NotificationId = Guid.NewGuid().ToString("N")[..8]
            },
            options);
    }

    /// <summary>
    /// 批量发送通知
    /// </summary>
    public async Task<List<NotificationSendResult>> SendBatchNotificationsAsync(
        List<BaseSyncNotification> notifications, 
        NotificationSendOptions? options = null)
    {
        var results = new List<NotificationSendResult>();
        var tasks = new List<Task<NotificationSendResult>>();

        foreach (var notification in notifications)
        {
            Task<NotificationSendResult> task = notification switch
            {
                ContactSyncCompletionNotification contactCompletion => 
                    SendContactSyncCompletedAsync(contactCompletion, options),
                GroupSyncCompletionNotification groupCompletion => 
                    SendGroupSyncCompletedAsync(groupCompletion, options),
                SyncProgressNotification progress when notification.WxManagerId != Guid.Empty => 
                    SendContactSyncProgressAsync(progress, options),
                _ => Task.FromResult(NotificationSendResult.CreateFailure("Unsupported notification type"))
            };

            tasks.Add(task);
        }

        results.AddRange(await Task.WhenAll(tasks));
        return results;
    }

    /// <summary>
    /// 检查通知服务健康状态
    /// </summary>
    public async Task<bool> CheckHealthAsync()
    {
        try
        {
            // 简单的健康检查：尝试发送测试通知
            await SendToSignalRAsync("HealthCheck", Guid.NewGuid(), new { Timestamp = DateTime.UtcNow });
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "❌ 通知服务健康检查失败");
            return false;
        }
    }

    /// <summary>
    /// 获取通知统计信息
    /// </summary>
    public async Task<NotificationStatistics> GetStatisticsAsync()
    {
        _statistics.LastUpdated = DateTime.UtcNow;
        return await Task.FromResult(_statistics);
    }

    #region 私有辅助方法

    /// <summary>
    /// 带重试机制的通知发送
    /// </summary>
    private async Task<NotificationSendResult> SendNotificationWithRetryAsync(
        string eventName,
        Guid wxManagerId,
        object data,
        NotificationSendOptions options)
    {
        var stopwatch = Stopwatch.StartNew();
        var retryCount = 0;
        Exception? lastException = null;

        await _sendSemaphore.WaitAsync();
        try
        {
            while (retryCount <= options.MaxRetryCount)
            {
                try
                {
                    await SendToSignalRAsync(eventName, wxManagerId, data);

                    // 更新统计信息
                    UpdateStatistics(true, stopwatch.ElapsedMilliseconds, retryCount, false);

                    return NotificationSendResult.CreateSuccess(
                        stopwatch.ElapsedMilliseconds,
                        retryCount,
                        false);
                }
                catch (Exception ex)
                {
                    lastException = ex;
                    retryCount++;

                    if (retryCount <= options.MaxRetryCount)
                    {
                        _logger.LogWarning("⚠️ 通知发送失败，准备重试 - Event: {EventName}, WxManagerId: {WxManagerId}, Retry: {Retry}/{Max}, Error: {Error}",
                            eventName, wxManagerId, retryCount, options.MaxRetryCount, ex.Message);

                        await Task.Delay(options.RetryIntervalMs * retryCount); // 指数退避
                    }
                }
            }

            // 所有重试都失败，尝试降级机制
            if (options.EnableFallback)
            {
                try
                {
                    await SendFallbackNotificationAsync(eventName, wxManagerId, data);

                    _logger.LogWarning("⚠️ 使用降级机制发送通知成功 - Event: {EventName}, WxManagerId: {WxManagerId}",
                        eventName, wxManagerId);

                    // 更新统计信息
                    UpdateStatistics(true, stopwatch.ElapsedMilliseconds, retryCount, true);

                    return NotificationSendResult.CreateSuccess(
                        stopwatch.ElapsedMilliseconds,
                        retryCount,
                        true);
                }
                catch (Exception fallbackEx)
                {
                    _logger.LogError(fallbackEx, "❌ 降级机制也失败 - Event: {EventName}, WxManagerId: {WxManagerId}",
                        eventName, wxManagerId);
                }
            }

            // 完全失败
            UpdateStatistics(false, stopwatch.ElapsedMilliseconds, retryCount, false);

            return NotificationSendResult.CreateFailure(
                lastException?.Message ?? "Unknown error",
                stopwatch.ElapsedMilliseconds,
                retryCount);
        }
        finally
        {
            _sendSemaphore.Release();
            stopwatch.Stop();
        }
    }

    /// <summary>
    /// 发送到SignalR
    /// </summary>
    private async Task SendToSignalRAsync(string eventName, Guid wxManagerId, object data)
    {
        try
        {
            // 尝试获取任何可用的Hub上下文
            var hubContext = _serviceProvider.GetService<IHubContext<Hub>>();
            if (hubContext != null)
            {
                var groupName = $"WxManager_{wxManagerId}";
                await hubContext.Clients.Group(groupName).SendAsync(eventName, data);

                _logger.LogDebug("📤 SignalR通知发送成功 - Event: {EventName}, Group: {GroupName}",
                    eventName, groupName);
                return;
            }

            // 如果没有找到Hub上下文，记录警告但不抛出异常
            _logger.LogWarning("⚠️ 未找到可用的SignalR Hub上下文 - Event: {EventName}", eventName);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "❌ SignalR通知发送失败 - Event: {EventName}", eventName);
            throw;
        }
    }

    /// <summary>
    /// 发送数据变更通知
    /// </summary>
    private async Task SendDataChangedNotificationAsync(Guid wxManagerId, string dataType, string refreshType)
    {
        try
        {
            await SendToSignalRAsync("DataChanged", wxManagerId, new
            {
                WxManagerId = wxManagerId.ToString(),
                DataType = dataType,
                RefreshType = refreshType,
                Timestamp = DateTime.UtcNow,
                Message = $"{dataType}同步完成"
            });

            _logger.LogDebug("📤 数据变更通知发送成功 - DataType: {DataType}, RefreshType: {RefreshType}",
                dataType, refreshType);
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "⚠️ 数据变更通知发送失败，但不影响主要通知");
        }
    }

    /// <summary>
    /// 降级通知机制（可以扩展为其他方式，如数据库记录、文件记录等）
    /// </summary>
    private async Task SendFallbackNotificationAsync(string eventName, Guid wxManagerId, object data)
    {
        // 这里可以实现降级机制，比如：
        // 1. 记录到数据库，让前端轮询
        // 2. 记录到文件系统
        // 3. 发送到消息队列
        // 目前简单记录日志

        _logger.LogWarning("🔄 执行降级通知机制 - Event: {EventName}, WxManagerId: {WxManagerId}, Data: {Data}",
            eventName, wxManagerId, System.Text.Json.JsonSerializer.Serialize(data));

        // 模拟降级处理
        await Task.Delay(100);
    }

    /// <summary>
    /// 防抖检查
    /// </summary>
    private bool ShouldDebounce(string key, TimeSpan interval)
    {
        var now = DateTime.UtcNow;
        if (_lastNotificationTimes.TryGetValue(key, out var lastTime))
        {
            if (now - lastTime < interval)
            {
                return true; // 需要防抖
            }
        }

        _lastNotificationTimes[key] = now;
        return false; // 不需要防抖
    }

    /// <summary>
    /// 更新统计信息
    /// </summary>
    private void UpdateStatistics(bool success, long elapsedMs, int retryCount, bool usedFallback)
    {
        _statistics.TotalSent++;

        if (success)
        {
            _statistics.SuccessCount++;
        }
        else
        {
            _statistics.FailureCount++;
        }

        _statistics.RetryCount += retryCount;

        if (usedFallback)
        {
            _statistics.FallbackCount++;
        }

        // 计算平均耗时
        _statistics.AverageElapsedMs = (_statistics.AverageElapsedMs * (_statistics.TotalSent - 1) + elapsedMs) / _statistics.TotalSent;
    }

    #endregion
}
